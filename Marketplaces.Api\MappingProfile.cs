using AutoMapper;
using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Dtos.Internal;
using Marketplaces.Api.Models;
using Marketplaces.Api.Models.Identity;
using Marketplaces.Api.Responses;

namespace Marketplaces.Api;

public class MappingProfile : Profile
{
    public MappingProfile()
    {
        CreateMap<Shop, ShopDto>()
            .ForMember(s => s.<PERSON>, member => member.MapFrom(s => s.OzonKey != null))
            .ForMember(s => s.Wb, member => member.MapFrom(s => s.WbKey != null))
            .ForMember(s => s.Yandex, member => member.MapFrom(s => s.YandexKey != null));

        CreateMap<Shop, InfoShopDto>()
            .ForPath(dto => dto.OzonMode, path => path.MapFrom(s => s.OzonMode))
            .ForPath(dto => dto.OzonTax, path => path.MapFrom(s => s.OzonTax))
            .ForPath(dto => dto.OzonEstimatedTax, path => path.MapFrom(s => s.OzonEstimatedTax))
            .ForPath(dto => dto.WildberriesMode, path => path.MapFrom(s => s.WildberriesMode))
            .ForPath(dto => dto.WildberriesTax, path => path.MapFrom(s => s.WildberriesTax))
            .ForPath(dto => dto.WildberriesEstimatedTax, path => path.MapFrom(s => s.WildberriesEstimatedTax));

        CreateMap<Nomenclature, NomenclatureDto>()
            .ForMember(dto => dto.Name, member => member.MapFrom(s => s.FindName()))
            .ForMember(dto => dto.Size, member => member.MapFrom(s => s.GetSize()));


        CreateMap<OzonNomenclature, NomenclatureDto.OzonDto>()
            .ForMember(dto => dto.RedemptionPercent, member =>
                member.Ignore())
            .ForMember(dto => dto.EstimatedTax, member =>
                member.Ignore());

        CreateMap<YandexNomenclature, NomenclatureDto.YandexDto>();
        CreateMap<WildberriesNomenclature, NomenclatureDto.WildberriesDto>()
            .ForMember(dto => dto.Code, member => member.MapFrom(s => s.Id))
            .ForMember(dto => dto.RedemptionPercent, member => member.MapFrom(s => s.RedemptionPercent));

        CreateMap<Nomenclature, StockDto>()
            .ForMember(dto => dto.Name, member => member.MapFrom(s => s.Name))
            .ForMember(dto => dto.MinAmount, member => member.MapFrom(s => s.GetMinFBS()))
            .ForMember(dto => dto.Size, member => member.MapFrom(s => s.GetSize()));

        CreateMap<Nomenclature, Responses.PriceDto>()
            .ForMember(dto => dto.Name, member => member.MapFrom(s => s.Name))
            .ForMember(dto => dto.MinAmount, member => member.MapFrom(s => s.GetMinFBS()))
            .ForMember(s => s.Size, member => member.MapFrom(s => s.GetSize()));


        CreateMap<OzonNomenclature, NestedPriceDto>()
            .ForMember(dto => dto.BasePrice, member => member.Ignore())
            .ForMember(dto => dto.DiscountPercent, member => member.Ignore())
            .ForMember(dto => dto.MinimumRevenue, member => member.MapFrom(s => s.MinimumRevenue))
            .ForMember(dto => dto.MinimumProfitPercentage, member => member.MapFrom(s => s.MinimumProfitPercentage));

        CreateMap<WildberriesNomenclature, NestedPriceDto>()
            .ForMember(dto => dto.BasePrice, member => member.MapFrom(s => s.Price))
            .ForMember(dto => dto.DiscountPercent, member => member.MapFrom(s => s.Discount))
            .ForMember(dto => dto.Price, member => member.Ignore())
            .ForMember(dto => dto.MarketingPrice, member => member.Ignore())
            .ForMember(dto => dto.MarketingSellerPrice, member => member.Ignore())
            .ForMember(dto => dto.MinPrice, member => member.Ignore())
            .ForMember(dto => dto.OldPrice, member => member.Ignore());


        CreateMap<YandexNomenclature, NestedPriceDto>()
            .ForMember(dto => dto.BasePrice, member => member.MapFrom(s => s.Price))
            .ForMember(dto => dto.DiscountPercent, member => member.Ignore())
            .ForMember(dto => dto.RedemptionPercent, member => member.Ignore())
            .ForMember(dto => dto.EstimatedTax, member => member.Ignore())
            .ForMember(dto => dto.Price, member => member.Ignore())
            .ForMember(dto => dto.MarketingPrice, member => member.Ignore())
            .ForMember(dto => dto.MarketingSellerPrice, member => member.Ignore())
            .ForMember(dto => dto.MinPrice, member => member.Ignore())
            .ForMember(dto => dto.OldPrice, member => member.Ignore());

        CreateMap<OzonNomenclature, MarketplaceStockDto>()
            .ForMember(s => s.FBS, member =>
                member.MapFrom(s => s.FBSAmount))
            .ForMember(s => s.FBO, member =>
                member.MapFrom(s => s.FBOAmount));

        CreateMap<WildberriesNomenclature, MarketplaceStockDto>()
            .ForMember(s => s.FBS, member =>
                member.MapFrom(s => s.FBSAmount))
            .ForMember(s => s.FBO, member =>
                member.MapFrom(s => s.FboAmount));

        CreateMap<YandexNomenclature, MarketplaceStockDto>()
            .ForMember(s => s.FBS, member =>
                member.MapFrom(s => s.FBSAmount))
            .ForMember(s => s.FBO, member => member.Ignore());

        CreateMap<ApplicationUser, UserDto>()
            .ForMember(a => a.IsEmailConfirmed, member => member.MapFrom(s => s.EmailConfirmed))
            .ForMember(a => a.WbToken, member =>
                member.MapFrom((s, _, _, ctx) => ((Shop)ctx.Items["Shop"]).WbKey != null))
            .ForMember(a => a.OzonToken, member =>
                member.MapFrom((s, _, _, ctx) => ((Shop)ctx.Items["Shop"]).OzonKey != null))
            .ForMember(a => a.YandexToken, member =>
                member.MapFrom((s, _, _, ctx) => ((Shop)ctx.Items["Shop"]).YandexKey != null))
            .ForMember(a => a.IsSubscriptionEnabled, member =>
                member.MapFrom((s, _, _, c) => ((Shop)c.Items["Shop"]).IsSubscriptionEnabled))
            .ForMember(a => a.SubscriptionEnd, member =>
                member.MapFrom((s, _, _, c) => ((Shop)c.Items["Shop"]).Subscriptions.FirstOrDefault()?.EndDate));

        CreateMap<OzonCommissionsDto, OzonPriceInfoDto>()
            .ForMember(dto => dto.Acquiring, member => member.Ignore())
            .ForMember(dto => dto.Price, member => member.Ignore())
            .ForMember(dto => dto.RedemptionAmount, member => member.Ignore())
            .ForMember(dto => dto.ReturnAmount, member => member.Ignore())
            // .ForMember(dto => dto.Promotes, member => member.Ignore())
            ;

        CreateMap<PriceItemDto, OzonPriceInfoDto>()
            .IncludeMembers(s => s.Commissions)
            .ForMember(dto => dto.Acquiring, member => member.MapFrom(s => s.Acquiring))
            .ForMember(dto => dto.Price, member => member.MapFrom(s => s.Price.MarketingSellerPrice))
            .ForMember(dto => dto.RedemptionAmount, member => member.Ignore())
            .ForMember(dto => dto.ReturnAmount, member => member.Ignore())
            // .ForMember(dto => dto.Promotes, member => member.Ignore())
            ;


        CreateMap<WildberriesNomenclature, WildberriesPriceInfoDto>()
            .ForMember(dto => dto.Price, member => member.MapFrom(s => s.Price))
            .ForMember(dto => dto.ToSendToSeller, member => member.MapFrom(s => s.ToSendToSellerSum))
            .ForMember(dto => dto.DeliveryCost, member => member.MapFrom(s => s.DeliveryPriceSum))
            .ForMember(dto => dto.Deduction, member => member.MapFrom(s => s.DeductionSum))
            .ForMember(dto => dto.Acceptance, member => member.MapFrom(s => s.AcceptanceSum))
            .ForMember(dto => dto.StorageFee, member => member.MapFrom(s => s.StorageFeeSum))
            .ForMember(dto => dto.Penalties, member => member.MapFrom(s => s.PenaltiesSum))
            .ForMember(dto => dto.RetailCost, member => member.MapFrom(s => s.RetailSum))
            .ForMember(dto => dto.RedemptionAmount, opt => 
                opt.MapFrom(s => s.RedemptionAmount));

        CreateMap<MarketingPromoteDto, PromoteDto>()
            .ForMember(dto => dto.Id, member => member.Ignore());


        CreateMap<Nomenclature, FullNomenclatureDto>()
            .ForMember(dto => dto.Name, member => member.MapFrom(s => s.Name))
            .ForMember(dto => dto.MinAmount, member => member.MapFrom(s => s.GetMinFBS()))
            .ForMember(s => s.Size, member => member.MapFrom(s => s.GetSize()));


        CreateMap<OzonNomenclature, FullInnerNomenclatureDto>()
            .ForMember(dto => dto.BasePrice, member => member.Ignore())
            .ForMember(dto => dto.DiscountPercent, member => member.Ignore())
            .ForMember(dto => dto.MinimumRevenue, member => member.MapFrom(s => s.MinimumRevenue))
            .ForMember(dto => dto.MinimumProfitPercentage, member => member.MapFrom(s => s.MinimumProfitPercentage))
            .ForMember(s => s.FbsAmount, member => member.MapFrom(s => s.FBSAmount))
            .ForMember(s => s.FboAmount, member => member.MapFrom(s => s.FBOAmount));
        ;

        CreateMap<WildberriesNomenclature, FullInnerNomenclatureDto>()
            .ForMember(dto => dto.BasePrice, member => member.MapFrom(s => s.Price))
            .ForMember(dto => dto.DiscountPercent, member => member.MapFrom(s => s.Discount))
            .ForMember(dto => dto.Price, member => member.Ignore())
            .ForMember(dto => dto.MarketingPrice, member => member.Ignore())
            .ForMember(dto => dto.MarketingSellerPrice, member => member.Ignore())
            .ForMember(dto => dto.MinPrice, member => member.Ignore())
            .ForMember(dto => dto.OldPrice, member => member.Ignore())
            .ForMember(s => s.FbsAmount, member => member.MapFrom(s => s.FBSAmount))
            .ForMember(s => s.FboAmount, member => member.MapFrom(s => s.FboAmount));
        ;


        CreateMap<YandexNomenclature, FullInnerNomenclatureDto>()
            .ForMember(dto => dto.BasePrice, member => member.MapFrom(s => s.Price))
            .ForMember(dto => dto.DiscountPercent, member => member.Ignore())
            .ForMember(dto => dto.RedemptionPercent, member => member.Ignore())
            .ForMember(dto => dto.EstimatedTax, member => member.Ignore())
            .ForMember(dto => dto.Price, member => member.Ignore())
            .ForMember(dto => dto.MarketingPrice, member => member.Ignore())
            .ForMember(dto => dto.MarketingSellerPrice, member => member.Ignore())
            .ForMember(dto => dto.MinPrice, member => member.Ignore())
            .ForMember(dto => dto.OldPrice, member => member.Ignore())
            .ForMember(s => s.FbsAmount, member =>
                member.MapFrom(s => s.FBSAmount))
            .ForMember(s => s.FboAmount, member => member.Ignore());
        
    }
}